package cn.iocoder.yudao.framework.tenant.core.context;

import cn.iocoder.yudao.framework.common.enums.DocumentEnum;
import cn.iocoder.yudao.framework.common.util.spring.SpringUtils;
import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 多租户上下文 Holder
 *
 * <AUTHOR>
 */
public class TenantContextHolder {

    /**
     * 当前租户编号
     */
    private static final ThreadLocal<Long> TENANT_ID = new TransmittableThreadLocal<>();

    /**
     * 是否忽略租户
     */
    private static final ThreadLocal<Boolean> IGNORE = new TransmittableThreadLocal<>();

    /**
     * 三方应用授权对接ERP机构(三方应用id)
     */
    private static final ThreadLocal<Long> THIRD_APP_ID = new TransmittableThreadLocal<>();

    /**
     * 租户上下文信息缓存
     */
    private static final ThreadLocal<Map<String, Object>> TENANT_CONTEXT_INFO = new TransmittableThreadLocal<>();

    /**
     * 租户上下文信息提供者
     */
    private static final List<TenantContextInfoProvider> tenantContextInfoProviders = new ArrayList<>();

    /**
     * 获得租户编号
     *
     * @return 租户编号
     */
    public static Long getTenantId() {
        return TENANT_ID.get();
    }

    /**
     * 获得租户编号。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 租户编号
     */
    public static Long getRequiredTenantId() {
        Long tenantId = getTenantId();
        if (tenantId == null) {
            throw new NullPointerException("TenantContextHolder 不存在租户编号！可参考文档："
                + DocumentEnum.TENANT.getUrl());
        }
        return tenantId;
    }

    public static void setTenantId(Long tenantId) {
        TENANT_ID.set(tenantId);
    }

    public static void setIgnore(Boolean ignore) {
        IGNORE.set(ignore);
    }

    /**
     * 当前是否忽略租户
     *
     * @return 是否忽略
     */
    public static boolean isIgnore() {
        return Boolean.TRUE.equals(IGNORE.get());
    }

    public static void setThirdAppId(Long thirdAppId) {
        THIRD_APP_ID.set(thirdAppId);
    }

    public static Long getThirdAppId() {
        return THIRD_APP_ID.get();
    }

    /**
     * 获取租户上下文信息
     *
     * @return 租户上下文信息
     */
    public static Map<String, Object> getTenantContextInfo() {
        if (TENANT_CONTEXT_INFO.get() != null) {
            return TENANT_CONTEXT_INFO.get();
        }

        // 如果设置了租户ID，同时加载租户相关信息
        if (tenantContextInfoProviders.isEmpty()) {
            Collection<TenantContextInfoProvider> providers = SpringUtils.getBeansOfType(TenantContextInfoProvider.class).values();
            // tenantContextInfoProvider 可能为null
            tenantContextInfoProviders.addAll(providers);
        }

        // 从租户上下文信息提供者中获取租户上下文信息
        Optional.ofNullable(getTenantId()).ifPresent(tenantId -> {
            // 过滤首个不为null的 tenantContextInfoProvider
            tenantContextInfoProviders.stream().filter(Objects::nonNull).findFirst()
                    .ifPresent(pvd -> TENANT_CONTEXT_INFO.set(pvd.getTenantInfo(tenantId)));
        });

        return TENANT_CONTEXT_INFO.get();
    }

    /**
     * 获取指定键的租户上下文信息
     *
     * @param key 信息键
     * @return 信息值
     */
    @SuppressWarnings("unchecked")
    public static <T> T getTenantContextInfo(String key) {
        Map<String, Object> info = getTenantContextInfo();
        return (T) info.get(key);
    }

    public static void clear() {
        TENANT_ID.remove();
        IGNORE.remove();
        THIRD_APP_ID.remove();
    }

}
